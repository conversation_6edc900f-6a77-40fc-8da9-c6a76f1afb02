新加一个公会充值活动类型1208。服务器数据需要记录每个活动期间各个公会充值的玩家id。
活动期间退出公会删除这个id。充值记录这个id到这个公会。同时通知所有在线的这个工会的玩家:act_guild_pay_info_s2c,加入了新公会旧的充值忽略。
读取配表ConfActGuildPay.pay_num充值人数，ConfActGuildPay.reward充值人数对应的奖励。

协议：message act_guild_pay_info_c2s {
   	option (msgid) = 6401;
   	uint32 act_type = 1;
   }

   message act_guild_pay_info_s2c {
   	option (msgid) = 6401;
   	uint32 act_type = 1;
   	uint32 count = 2;
   	repeated uint32 got_reward = 3 [packed=false];
   }

   message act_guild_pay_get_reward_c2s {
   	option (msgid) = 6402;
   	uint32 act_type = 1;
   	uint32 reward_id = 2;
   }