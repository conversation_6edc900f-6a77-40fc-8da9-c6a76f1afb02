package org.gof.demo.worldsrv.activity.data.controldata;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.character.HumanObject;

import java.util.ArrayList;
import java.util.List;

/**
 * 公会充值活动数据
 */
public class ControlGuildPayData implements IControlData {
    
    /**
     * 已领取的奖励ID列表
     */
    public List<Integer> gotRewardList = new ArrayList<>();

    public ControlGuildPayData() {
    }

    public ControlGuildPayData(String json) {
        JSONObject jo = Utils.toJSONObject(json);
        gotRewardList = Utils.strToIntList(jo.getString("gotRewardList"));
    }

    @Override
    public void create(HumanObject humanObj, ActivityVo vo) {
        // 初始化时不需要特殊处理
    }

    @Override
    public String toJSON() {
        JSONObject jo = new JSONObject();
        jo.put("gotRewardList", Utils.intListToStr(gotRewardList));
        return jo.toString();
    }
}
