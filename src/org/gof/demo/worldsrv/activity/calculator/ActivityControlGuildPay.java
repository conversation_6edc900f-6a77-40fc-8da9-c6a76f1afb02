package org.gof.demo.worldsrv.activity.calculator;

import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.activity.data.controldata.ControlGuildPayData;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfActGuildPay;
import org.gof.demo.worldsrv.config.ConfActivityTerm;
import org.gof.demo.worldsrv.guild.GuildManager;
import org.gof.demo.worldsrv.guild.GuildServiceProxy;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.msg.MsgAct2;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 公会充值活动控制器
 */
public class ActivityControlGuildPay extends AbstractActivityControl {

    public ActivityControlGuildPay(int type) {
        super(type);
    }

    @Override
    public void sendActivityData(HumanObject humanObj) {
        super.sendActivityData(humanObj);
        on_act_guild_pay_info_c2s(humanObj);
    }

    /**
     * 处理公会充值活动信息请求
     */
    public void on_act_guild_pay_info_c2s(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        
        ControlGuildPayData guildPayData = (ControlGuildPayData) data.getControlData();
        if (guildPayData == null) {
            Log.game.error("公会充值活动数据为空，humanId={}, actType={}", humanObj.id, type);
            return;
        }

        // 获取公会充值人数
        int payCount = getGuildPayCount(humanObj);
        
        MsgAct2.act_guild_pay_info_s2c.Builder msg = MsgAct2.act_guild_pay_info_s2c.newBuilder();
        msg.setActType(type);
        msg.setCount(payCount);
        msg.addAllGotReward(guildPayData.gotRewardList);
        
        humanObj.sendMsg(msg);
    }

    /**
     * 处理公会充值活动奖励领取
     */
    public void on_act_guild_pay_get_reward_c2s(HumanObject humanObj, int rewardId) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        
        ControlGuildPayData guildPayData = (ControlGuildPayData) data.getControlData();
        if (guildPayData == null) {
            Log.game.error("公会充值活动数据为空，humanId={}, actType={}", humanObj.id, type);
            return;
        }

        // 检查是否已经领取过
        if (guildPayData.gotRewardList.contains(rewardId)) {
            Log.game.error("公会充值活动奖励已领取，humanId={}, actType={}, rewardId={}", humanObj.id, type, rewardId);
            return;
        }

        // 获取活动配置
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confTerm == null) {
            Log.game.error("活动期数配置不存在，humanId={}, actType={}, round={}", humanObj.id, type, data.getActControlData().getRound());
            return;
        }

        // 获取公会充值配置
        ConfActGuildPay confGuildPay = getGuildPayConf(confTerm.group_id, rewardId);
        if (confGuildPay == null) {
            Log.game.error("公会充值配置不存在，humanId={}, actType={}, groupId={}, rewardId={}", 
                    humanObj.id, type, confTerm.group_id, rewardId);
            return;
        }

        // 检查公会充值人数是否达到要求
        int payCount = getGuildPayCount(humanObj);
        if (payCount < confGuildPay.pay_num) {
            Log.game.error("公会充值人数不足，humanId={}, actType={}, payCount={}, needCount={}", 
                    humanObj.id, type, payCount, confGuildPay.pay_num);
            return;
        }

        // 发放奖励
        ProduceManager.inst().produceAdd(humanObj, confGuildPay.reward, MoneyItemLogKey.公会充值活动);
        InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, confGuildPay.reward);

        // 记录已领取
        guildPayData.gotRewardList.add(rewardId);
        data.updateControlData();

        Log.game.info("公会充值活动奖励领取成功，humanId={}, actType={}, rewardId={}", humanObj.id, type, rewardId);
        
        // 发送更新消息
        on_act_guild_pay_info_c2s(humanObj);
    }

    /**
     * 处理充值回调
     */
    @Override
    public void pay(HumanObject humanObj, int payMallSn) {
        // 检查玩家是否在公会中
        if (humanObj.guildId <= 0) {
            return;
        }

        // 记录充值到公会数据中
        recordGuildPay(humanObj);
        
        // 通知公会所有在线成员
        notifyGuildMembers(humanObj);
    }

    /**
     * 获取公会充值人数
     */
    private int getGuildPayCount(HumanObject humanObj) {
        if (humanObj.guildId <= 0) {
            return 0;
        }

        // 通过公会服务获取充值人数
        GuildServiceProxy proxy = GuildServiceProxy.newInstance();
        proxy.getGuildPayCount(humanObj.guildId, type);
        proxy.listenResult(this::_result_getGuildPayCount, "humanObj", humanObj);

        // 这里暂时返回0，实际值会通过回调获取
        return 0;
    }

    /**
     * 公会充值人数查询回调
     */
    private void _result_getGuildPayCount(Object result, Object... objects) {
        HumanObject humanObj = (HumanObject) objects[1];
        if (result instanceof Integer) {
            int payCount = (Integer) result;
            // 发送更新的活动信息
            on_act_guild_pay_info_c2s(humanObj);
        }
    }

    /**
     * 获取公会充值配置
     */
    private ConfActGuildPay getGuildPayConf(int groupId, int rewardId) {
        Collection<ConfActGuildPay> confs = ConfActGuildPay.findBy(
                ConfActGuildPay.K.act_type, type,
                ConfActGuildPay.K.group_id, groupId
        );

        for (ConfActGuildPay conf : confs) {
            if (conf.sn == rewardId) {
                return conf;
            }
        }
        return null;
    }

    /**
     * 记录公会充值
     */
    private void recordGuildPay(HumanObject humanObj) {
        // 调用公会服务记录充值
        GuildServiceProxy proxy = GuildServiceProxy.newInstance();
        proxy.recordGuildPay(humanObj.guildId, humanObj.id, type);
        proxy.listenResult(this::_result_recordGuildPay, "humanObj", humanObj);

        Log.game.info("记录公会充值，humanId={}, guildId={}, actType={}", humanObj.id, humanObj.guildId, type);
    }

    /**
     * 记录公会充值回调
     */
    private void _result_recordGuildPay(Object result, Object... objects) {
        HumanObject humanObj = (HumanObject) objects[1];
        if (result instanceof Boolean && (Boolean) result) {
            Log.game.info("公会充值记录成功，humanId={}, guildId={}, actType={}", humanObj.id, humanObj.guildId, type);
        }
    }

    /**
     * 通知公会成员
     */
    private void notifyGuildMembers(HumanObject humanObj) {
        // 通过公会服务通知所有在线成员
        GuildServiceProxy proxy = GuildServiceProxy.newInstance();
        proxy.notifyGuildPayUpdate(humanObj.guildId, type);

        Log.game.info("通知公会成员充值，humanId={}, guildId={}, actType={}", humanObj.id, humanObj.guildId, type);
    }

    /**
     * 处理玩家退出公会
     */
    public void onPlayerLeaveGuild(HumanObject humanObj, long guildId) {
        // 从公会充值记录中移除该玩家
        GuildServiceProxy proxy = GuildServiceProxy.newInstance();
        proxy.removeGuildPayRecord(guildId, humanObj.id, type);

        Log.game.info("玩家退出公会，移除充值记录，humanId={}, guildId={}, actType={}", humanObj.id, guildId, type);
    }
}
