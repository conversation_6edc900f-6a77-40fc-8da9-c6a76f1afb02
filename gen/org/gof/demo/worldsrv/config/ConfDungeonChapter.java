package org.gof.demo.worldsrv.config;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.commons.lang3.StringUtils;
import org.gof.core.support.ConfigJSON;
import org.gof.core.support.IReloadSupport;
import org.gof.core.support.OrderBy;
import org.gof.core.support.OrderByField;
import org.gof.core.support.SysException;
import org.gof.core.support.Utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.gof.demo.worldsrv.support.Log;

/**
 * DungeonChapter_转剑关卡表 
 * DungeonChapter_转剑关卡表.xlsx
 * <AUTHOR>
 * 此类是系统自动生成类 不要直接修改，修改后也会被覆盖
 */
@ConfigJSON
public class ConfDungeonChapter {
	public final int sn;			//sn
	public final int order;			//关卡顺位
	public final int group;			//所属难度
	public final int cost;			//体力消耗
	public final int exp;			//奖励经验值
	public final int fast_need_power;			//碾压所需战力
	public final int[] fast_gold;			//扫荡获取金币数
	public final int[] fast_reward_id;			//扫荡奖励id池
	public final int[] fast_reward_min;			//扫荡奖励数量最低值
	public final int[] fast_reward_max;			//扫荡奖励数量最高值
	public final int[] fast_reward_weight;			//扫荡奖励权重
	public final String power;			//战力验证？
	public final int level;			//关卡等级
	public final int pre_dungeon;			//前置关卡
	public final int next_dungeon;			//后续关卡
	public final int unlock_type;			//解锁条件类型。1=等级达到x
	public final int unlock_value;			//解锁条件值
	public final int layer;			//关卡层数
	public final int factor;			//关卡系数，影像怪物强度
	public final int event_group;			//事件组id，从该组中随机生成各事件
	public final int stone_initial;			//进入关卡的初始魔石数
	public final int[] stone_cost;			//吞噬魔石消耗。依次配置，超额取最后值
	public final int buff_refresh_item;			//刷新消耗道具id
	public final int[] buff_refresh_cost;			//刷新消耗数量
	public final int[] clear_reward;			//敌全灭奖励的魔石数量。[第一层|增量]
	public final int equip_num;			//解锁的装备格子数
	public final int layer_points;			//到达层数转化评分
	public final int area_points;			//探索区域转化评分
	public final int enemy_points;			//杀怪数转化评分
	public final int boss_points;			//BOSS转化评分
	public final int[] attr_points;			//血、攻、防转化评分
	public final int base_gold;			//关卡基础金币
	public final int gold_factor;			//积分转换金币系数，万分比值
	public final int coin;			//关卡结算时获得的冒险功勋数量。道具id固定1877
	

	public ConfDungeonChapter(int sn, int order, int group, int cost, int exp, int fast_need_power, int[] fast_gold, int[] fast_reward_id, int[] fast_reward_min, int[] fast_reward_max, int[] fast_reward_weight, String power, int level, int pre_dungeon, int next_dungeon, int unlock_type, int unlock_value, int layer, int factor, int event_group, int stone_initial, int[] stone_cost, int buff_refresh_item, int[] buff_refresh_cost, int[] clear_reward, int equip_num, int layer_points, int area_points, int enemy_points, int boss_points, int[] attr_points, int base_gold, int gold_factor, int coin) {
		this.sn = sn;
		this.order = order;
		this.group = group;
		this.cost = cost;
		this.exp = exp;
		this.fast_need_power = fast_need_power;
		this.fast_gold = fast_gold;
		this.fast_reward_id = fast_reward_id;
		this.fast_reward_min = fast_reward_min;
		this.fast_reward_max = fast_reward_max;
		this.fast_reward_weight = fast_reward_weight;
		this.power = power;
		this.level = level;
		this.pre_dungeon = pre_dungeon;
		this.next_dungeon = next_dungeon;
		this.unlock_type = unlock_type;
		this.unlock_value = unlock_value;
		this.layer = layer;
		this.factor = factor;
		this.event_group = event_group;
		this.stone_initial = stone_initial;
		this.stone_cost = stone_cost;
		this.buff_refresh_item = buff_refresh_item;
		this.buff_refresh_cost = buff_refresh_cost;
		this.clear_reward = clear_reward;
		this.equip_num = equip_num;
		this.layer_points = layer_points;
		this.area_points = area_points;
		this.enemy_points = enemy_points;
		this.boss_points = boss_points;
		this.attr_points = attr_points;
		this.base_gold = base_gold;
		this.gold_factor = gold_factor;
		this.coin = coin;
	}

	private static IReloadSupport support = null;
	
	public static void initReloadSupport(IReloadSupport s){
		support = s;
	}
	
	public static void reLoad() {
		if(support != null)
			support.beforeReload();
		DATA._init();
		
		if(support != null)
			support.afterReload();
	}
	
	/**
	 * 获取全部数据
	 * @return
	 */
	public static Collection<ConfDungeonChapter> findAll() {
		return DATA.getList();
	}

	/**
	 * 通过SN获取数据
	 * @param sn
	 * @return
	 */
	public static ConfDungeonChapter get(Integer sn) {
		return DATA.getMap().get(sn);
	}
	
	/**
	 * 通过多个组件组合成sn获取数据
	 * @param obj 多组件按顺序
	 * @return ConfDungeonChapter 对象
	 */

	
	/**
	 * 通过属性获取单条数据
	 * @param params
	 * @return
	 */
	public static ConfDungeonChapter getBy(Object...params) {
		List<ConfDungeonChapter> list = utilBase(params);
		
		if(list.isEmpty()) return null;
		else return list.get(0);
	}
	
	/**
	 * 通过属性获取数据集合
	 * @param params
	 * @return
	 */
	public static List<ConfDungeonChapter> findBy(Object...params) {
		return utilBase(params);
	}
	
	/**
	 * 通过属性获取数据集合 支持排序
	 * @param params
	 * @return
	 */
	public static List<ConfDungeonChapter> utilBase(Object...params) {
		List<Object> settings = Utils.ofList(params);
		
		//查询参数
		final Map<String, Object> paramsFilter = new LinkedHashMap<>();		//过滤条件
		final List<OrderByField> paramsOrder = new ArrayList<>();		//排序规则
				
		//参数数量
		int len = settings.size();
		
		//参数必须成对出现
		if(len % 2 != 0) {
			throw new SysException("查询参数必须成对出现:query={}", settings);
		}
		
		//处理成对参数
		for(int i = 0; i < len; i += 2) {
			String key = (String)settings.get(i);
			Object val = settings.get(i + 1);
			
			//参数 排序规则
			if(val instanceof OrderBy) {
				paramsOrder.add(new OrderByField(key, (OrderBy) val));
			} else {	//参数 过滤条件
				paramsFilter.put(key, val);
			}
		}
		
		//返回结果
		List<ConfDungeonChapter> result = new ArrayList<>();
		
		try {
			//通过条件获取结果
			for(ConfDungeonChapter c : DATA.getList()) {
				//本行数据是否符合过滤条件
				boolean bingo = true;
				
				//判断过滤条件
				for(Entry<String, Object> p : paramsFilter.entrySet()) {
					//实际结果
					Object valTrue = c.getFieldValue(p.getKey());
					//期望结果
					Object valWish = p.getValue();
					
					//有不符合过滤条件的
					if(!valWish.equals(valTrue)) {
						bingo = false;
						break;
					}
				}
				
				//记录符合结果
				if(bingo) {
					result.add(c);
				}
			}
		} catch (Exception e) {
			throw new SysException(e);
		}
		
		//对结果进行排序
		Collections.sort(result, (a, b) -> a.compareTo(b, paramsOrder));
		
		return result;
	}

	/**
	 * 属性关键字
	 */
	public static final class K {
		public static final String sn = "sn";	//sn
		public static final String order = "order";	//关卡顺位
		public static final String group = "group";	//所属难度
		public static final String cost = "cost";	//体力消耗
		public static final String exp = "exp";	//奖励经验值
		public static final String fast_need_power = "fast_need_power";	//碾压所需战力
		public static final String fast_gold = "fast_gold";	//扫荡获取金币数
		public static final String fast_reward_id = "fast_reward_id";	//扫荡奖励id池
		public static final String fast_reward_min = "fast_reward_min";	//扫荡奖励数量最低值
		public static final String fast_reward_max = "fast_reward_max";	//扫荡奖励数量最高值
		public static final String fast_reward_weight = "fast_reward_weight";	//扫荡奖励权重
		public static final String power = "power";	//战力验证？
		public static final String level = "level";	//关卡等级
		public static final String pre_dungeon = "pre_dungeon";	//前置关卡
		public static final String next_dungeon = "next_dungeon";	//后续关卡
		public static final String unlock_type = "unlock_type";	//解锁条件类型。1=等级达到x
		public static final String unlock_value = "unlock_value";	//解锁条件值
		public static final String layer = "layer";	//关卡层数
		public static final String factor = "factor";	//关卡系数，影像怪物强度
		public static final String event_group = "event_group";	//事件组id，从该组中随机生成各事件
		public static final String stone_initial = "stone_initial";	//进入关卡的初始魔石数
		public static final String stone_cost = "stone_cost";	//吞噬魔石消耗。依次配置，超额取最后值
		public static final String buff_refresh_item = "buff_refresh_item";	//刷新消耗道具id
		public static final String buff_refresh_cost = "buff_refresh_cost";	//刷新消耗数量
		public static final String clear_reward = "clear_reward";	//敌全灭奖励的魔石数量。[第一层|增量]
		public static final String equip_num = "equip_num";	//解锁的装备格子数
		public static final String layer_points = "layer_points";	//到达层数转化评分
		public static final String area_points = "area_points";	//探索区域转化评分
		public static final String enemy_points = "enemy_points";	//杀怪数转化评分
		public static final String boss_points = "boss_points";	//BOSS转化评分
		public static final String attr_points = "attr_points";	//血、攻、防转化评分
		public static final String base_gold = "base_gold";	//关卡基础金币
		public static final String gold_factor = "gold_factor";	//积分转换金币系数，万分比值
		public static final String coin = "coin";	//关卡结算时获得的冒险功勋数量。道具id固定1877
	}
	
	/**
	 *
	 * 取得属性值
	 * @param classInstance 实例
	 * @key 属性名称
	 *
	 */
	@SuppressWarnings("unchecked")
	public <T> T getFieldValue(String key) {
		Object value = null;
		switch (key) {
			case "sn": {
				value = this.sn;
				break;
			}
			case "order": {
				value = this.order;
				break;
			}
			case "group": {
				value = this.group;
				break;
			}
			case "cost": {
				value = this.cost;
				break;
			}
			case "exp": {
				value = this.exp;
				break;
			}
			case "fast_need_power": {
				value = this.fast_need_power;
				break;
			}
			case "fast_gold": {
				value = this.fast_gold;
				break;
			}
			case "fast_reward_id": {
				value = this.fast_reward_id;
				break;
			}
			case "fast_reward_min": {
				value = this.fast_reward_min;
				break;
			}
			case "fast_reward_max": {
				value = this.fast_reward_max;
				break;
			}
			case "fast_reward_weight": {
				value = this.fast_reward_weight;
				break;
			}
			case "power": {
				value = this.power;
				break;
			}
			case "level": {
				value = this.level;
				break;
			}
			case "pre_dungeon": {
				value = this.pre_dungeon;
				break;
			}
			case "next_dungeon": {
				value = this.next_dungeon;
				break;
			}
			case "unlock_type": {
				value = this.unlock_type;
				break;
			}
			case "unlock_value": {
				value = this.unlock_value;
				break;
			}
			case "layer": {
				value = this.layer;
				break;
			}
			case "factor": {
				value = this.factor;
				break;
			}
			case "event_group": {
				value = this.event_group;
				break;
			}
			case "stone_initial": {
				value = this.stone_initial;
				break;
			}
			case "stone_cost": {
				value = this.stone_cost;
				break;
			}
			case "buff_refresh_item": {
				value = this.buff_refresh_item;
				break;
			}
			case "buff_refresh_cost": {
				value = this.buff_refresh_cost;
				break;
			}
			case "clear_reward": {
				value = this.clear_reward;
				break;
			}
			case "equip_num": {
				value = this.equip_num;
				break;
			}
			case "layer_points": {
				value = this.layer_points;
				break;
			}
			case "area_points": {
				value = this.area_points;
				break;
			}
			case "enemy_points": {
				value = this.enemy_points;
				break;
			}
			case "boss_points": {
				value = this.boss_points;
				break;
			}
			case "attr_points": {
				value = this.attr_points;
				break;
			}
			case "base_gold": {
				value = this.base_gold;
				break;
			}
			case "gold_factor": {
				value = this.gold_factor;
				break;
			}
			case "coin": {
				value = this.coin;
				break;
			}
			default: break;
		}
		
		return (T) value;
	}

	/**
	 * 数据集
	 * 单独提出来也是为了做数据延迟初始化
	 * 避免启动遍历类时，触发了static静态块
	 */
	@SuppressWarnings({"unused"})
	private static final class DATA {
		//全部数据
		private static Map<Integer, ConfDungeonChapter> _map;
		
		/**
		 * 获取数据的值集合
		 * @return
		 */
		public static Collection<ConfDungeonChapter> getList() {
			return getMap().values();
		}
		
		/**
		 * 获取Map类型数据集合
		 * @return
		 */
		public static Map<Integer, ConfDungeonChapter> getMap() {
			//延迟初始化
			if(_map == null) {
				synchronized (DATA.class) {
					if(_map == null) {
						_init();
					}
				}
			}
			
			return _map;
		}


		/**
		 * 初始化数据
		 */
		private static void _init() {
			Map<Integer, ConfDungeonChapter> dataMap = new java.util.concurrent.ConcurrentHashMap<>();
			
			//JSON数据
			String confJSON = _readConfFile();
			if(StringUtils.isBlank(confJSON)) return;
			
			//填充实体数据
			JSONArray confs = (JSONArray)JSONArray.parse(confJSON);
			
			JSONObject confTemp = confs.getJSONObject(0);
			if(!confTemp.containsKey("sn")){
				Log.temp.info("表{}有问题!", "ConfDungeonChapter");
			}
			for(int i = 0 ; i < confs.size() ; i++){
				JSONObject conf = confs.getJSONObject(i);
				
				/*if(conf.get("sn") instanceof String && conf.get("sn") == null){
					continue;
				}*/
				ConfDungeonChapter object = new ConfDungeonChapter(conf.getIntValue("sn"), conf.getIntValue("order"), conf.getIntValue("group"), conf.getIntValue("cost"), 
				conf.getIntValue("exp"), conf.getIntValue("fast_need_power"), parseIntArray(conf.getString("fast_gold")), parseIntArray(conf.getString("fast_reward_id")), 
				parseIntArray(conf.getString("fast_reward_min")), parseIntArray(conf.getString("fast_reward_max")), parseIntArray(conf.getString("fast_reward_weight")), conf.getString("power"), 
				conf.getIntValue("level"), conf.getIntValue("pre_dungeon"), conf.getIntValue("next_dungeon"), conf.getIntValue("unlock_type"), 
				conf.getIntValue("unlock_value"), conf.getIntValue("layer"), conf.getIntValue("factor"), conf.getIntValue("event_group"), 
				conf.getIntValue("stone_initial"), parseIntArray(conf.getString("stone_cost")), conf.getIntValue("buff_refresh_item"), parseIntArray(conf.getString("buff_refresh_cost")), 
				parseIntArray(conf.getString("clear_reward")), conf.getIntValue("equip_num"), conf.getIntValue("layer_points"), conf.getIntValue("area_points"), 
				conf.getIntValue("enemy_points"), conf.getIntValue("boss_points"), parseIntArray(conf.getString("attr_points")), conf.getIntValue("base_gold"), 
				conf.getIntValue("gold_factor"), conf.getIntValue("coin"));
				if(!conf.containsKey("sn")){
					
				    continue;
                }
				dataMap.put(conf.getInteger("sn"), object);
			}

			//保存数据
			_map = Collections.unmodifiableMap(dataMap);
		}
		
		
		
		public static double[] parseDoubleArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			String[] elems = value.split("\\|");
			if(value.contains(",")){
				elems = value.split("\\,");
			}
			if(elems.length > 0) {
				double []temp = new double[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = Utils.doubleValue(elems[i]);
				}
				return temp;
			}
			return null;
	  }
	  
		public static float[] parseFloatArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			String[] elems = value.split("\\|");
			if(value.contains(",")){
				elems = value.split("\\,");
			}
			if(elems.length > 0) {
				float []temp = new float[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = Utils.floatValue(elems[i]);
				}
				return temp;
			}
			return null;
		}
		
		public static int[] parseIntArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return  null;
			}
			String[] elems = value.split("\\|");
			if(value.contains(",")){
				elems = value.split("\\,");
			}
			if(elems.length > 0) {
				int []temp = new int[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = Utils.intValue(elems[i]);
				}
				return temp;
			}
			return null;
		}
		
		public static float[][] parseFloatArray2(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			
			float[][] elems = null;
			String [] strArr = Utils.splitStr(value, "\\|");
			if(strArr.length > 0){
				for(int i = 0; i < strArr.length; i++){
					float[] floatArr = Utils.strToFloatArray(strArr[i]);
					if(elems == null){
						elems = new float[strArr.length][floatArr.length];
					}
					elems[i] = new float[floatArr.length];
					for(int m = 0; m < floatArr.length; m++) {
						elems[i][m]=floatArr[m];
					}
				}
				return elems;
			}
			return null;
		}
		
		public static int[][] parseIntArray2(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			
			int[][] elems = null;
			String [] strArr = Utils.splitStr(value, "\\|");
			if(strArr.length > 0){
				for(int i = 0; i < strArr.length; i++){
					int[] intArr = Utils.arrayStrToInt(strArr[i]);
					if(elems == null){
						elems = new int[strArr.length][intArr.length];
					}
					elems[i] = new int[intArr.length];
					for(int m = 0; m < intArr.length; m++) {
						elems[i][m]=intArr[m];
					}
				}
				return elems;
			}
			return null;
		}
		
	
		public static String[] parseStringArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			String[] elems = value.split("\\|");
			//if(value.contains(",")){
			//	elems = value.split("\\,");
			//}
			if(elems.length > 0) {
				String []temp = new String[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = elems[i];
				}
				return temp;
			}
			return null;
		}
		
		public static String[][] parseStringArray2(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}

			String[][] elems = null;
			String[] strArr = Utils.splitStr(value, "\\|");
			if(strArr.length > 0){
				for(int i = 0; i < strArr.length; i++){
					String[] arr = Utils.splitStr(strArr[i], "\\,");
					if(elems == null){
						elems = new String[strArr.length][arr.length];
					}
					elems[i] = new String[arr.length];
					for(int m = 0; m < arr.length; m++) {
						elems[i][m]=arr[m];
					}
				}
				return elems;
			}
			return null;
		}
		
		public static long[] parseLongArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			String[] elems = value.split("\\|");
			if(value.contains(",")){
				elems = value.split("\\,");
			}
			if(elems.length > 0) {
				long []temp = new long[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = Utils.longValue(elems[i]);
				}
				return temp;
			}
			return null;
		}
		
		public static long[][] parseLongArray2(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			
			long[][] elems = null;
			String [] strArr = Utils.splitStr(value, "\\|");
			if(strArr.length > 0){
				for(int i = 0; i < strArr.length; i++){
					long[] longArr = Utils.arrayStrToLong(strArr[i]);
					if(elems == null){
						elems = new long[strArr.length][longArr.length];
					}
					elems[i] = new long[longArr.length];
					for(int m = 0; m < longArr.length; m++) {
						elems[i][m]=longArr[m];
					}
				}
				return elems;
			}
			return null;
		}
		
		public static boolean[] parseBoolArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
		
			String[] elems = value.split("\\|");
			if(value.contains(",")){
				elems = value.split("\\,");
			}
			if(elems.length > 0) {
				boolean []temp = new boolean[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = Utils.booleanValue(elems[i]);
				}
				return temp;
			}
			return null;
		}
	
		/**
		 * 读取游戏配置
		 */
		private static String _readConfFile() {
			String result = "";
			BufferedReader reader = null;
			
			try {
				String filePath = Thread.currentThread().getContextClassLoader().getResource("ConfDungeonChapter.json").getPath();
				InputStream is = new FileInputStream(filePath);
				
				reader = new BufferedReader(new InputStreamReader(is , "UTF-8"));
			    String tempString = "";
			    while ((tempString = reader.readLine()) != null) {
				result += tempString;
			    }
			    
			} catch (Exception e) {
			    throw new RuntimeException(e);
			} finally {
				if(reader != null)
					try {
						reader.close();
					} catch (IOException e) {
						throw new RuntimeException(e);
					}
			}

			return result;
		}
	}
	
	/**
	 * 比较函数
	 * 
	 * @param cell 比较的对象
	 * @param params 自定义排序字段
	 * @return
	 */
	public int compare(ConfDungeonChapter cell, Object...params) {
		List<Object> settings = Utils.ofList(params);
		List<OrderByField> paramsOrder = new ArrayList<>();		//排序规则
		
		//参数数量
		int len = settings.size();
		
		//参数必须成对出现
		if(len % 2 != 0) {
			throw new SysException("查询参数必须成对出现:query={}", settings);
		}
		
		//处理成对参数
		for(int i = 0; i < len; i += 2) {
			String key = (String)settings.get(i);
			Object val = settings.get(i + 1);
			
			//参数 排序规则
			if(val instanceof OrderBy) {
				paramsOrder.add(new OrderByField(key, (OrderBy) val));
			}
		}
		
		return compareTo(cell, paramsOrder);
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	private int compareTo(ConfDungeonChapter cell, List<OrderByField> paramsOrder) {
		try {
			for(OrderByField e : paramsOrder) {
				//两方字段值
				Comparable va = this.getFieldValue(e.getKey());
				Comparable vb = cell.getFieldValue(e.getKey());
				
				//值排序结果
				int compareResult = va.compareTo(vb);
				
				//相等时 根据下一个值进行排序
				if(va.compareTo(vb) == 0) continue;
				
				//配置排序规则
				OrderBy order = e.getOrderBy();
				if(order == OrderBy.ASC) return compareResult;		//正序
				else return -1 * compareResult;					//倒序
			}
		} catch (Exception e) {
			throw new SysException(e);
		}

		return 0;
	}
    
}